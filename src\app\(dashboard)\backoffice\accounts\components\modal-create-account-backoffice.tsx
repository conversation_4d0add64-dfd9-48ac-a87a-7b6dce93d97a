import { Input } from "@/components/global/inputs/input";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { Flex, HStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import BasicBackofficeModal from "@/components/global/modal/basic-backoffice-modal";
import { InputSelect } from "@/components/global/inputs/input-select";
import useGetPlans from "@/hook/plans/useGetPlans";

type ModalCreateAccountBackofficeProps = {
  openModal: boolean;
  setOpenModal: (value: boolean) => void;
};

const createAccountSchema = yup.object().shape({
  companyName: yup.string().required("Nome da empresa é obrigatório"),
  name: yup.string().required("Nome é obrigatório"),
  email: yup.string().email("Email inválido").required("Email é obrigatório"),
  cellPhone: yup.string(),
  password: yup.string().required("Senha é obrigatória"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem coincidir")
    .required("Confirmação de senha é obrigatória"),
  cpf: yup.string().required("CPF é obrigatório"),
  planSecureId: yup.array(yup.string()).optional(),
});

type CreateAccountFormData = yup.InferType<typeof createAccountSchema>;

export default function ModalCreateAccountBackoffice({
  openModal,
  setOpenModal,
}: ModalCreateAccountBackofficeProps) {
  const [phoneMask, setPhoneMask] = useState("+99 (99) 9999-9999[9]");
  const { data: plans } = useGetPlans(true);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateAccountFormData>({
    resolver: yupResolver(createAccountSchema),
  });

  const watchCellphone = watch("cellPhone");

  useEffect(() => {
    if (watchCellphone) {
      const onlyNumbers = watchCellphone.replace(/\D/g, "");
      if (onlyNumbers.length === 13) {
        setPhoneMask("+99 (99) 99999-9999");
      } else {
        setPhoneMask("+99 (99) 9999-9999[9]");
      }
    }
  }, [watchCellphone]);

  const createAccount = useMutation({
    mutationFn: async (data: CreateAccountFormData) => {
      const { planSecureId, ...rest } = data;
      await api.post("/register/backoffice-account", {
        planSecureId: planSecureId ? planSecureId[0] : null,
        ...rest,
      });
    },
    onSuccess: () => {
      toaster.create({
        description: "Conta criada com Sucesso.",
        title: "Conta criada!",
        type: "success",
      });
      queryClient.invalidateQueries({
        queryKey: ["accounts-backoffice"],
      });
      setOpenModal(false);
      reset();
    },
    onError: () => {},
  });

  const onSubmit = async (data: CreateAccountFormData) => {
    try {
      createAccount.mutateAsync(data);
    } catch (e) {}
  };

  return (
    <BasicBackofficeModal
      open={openModal}
      size="xl"
      setOpen={setOpenModal}
      cancelText="Voltar"
      confirmText="Cadastrar"
      asForm
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit(onSubmit)}
      title="Criar nova conta"
      placement="top"
      children={
      <Flex flex={1} gap={2} flexDir={"column"} alignItems={"center"}>
        <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
          <Input
            label="Nome da Empresa"
            color="background"
            placeholder="Digite o nome da empresa"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("companyName")}
            error={errors.companyName}
          />
          <Input
            label="Nome do Usuário"
            color="background"
            placeholder="Digite o nome do usuário"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("name")}
            error={errors.name}
          />
        </HStack>
        <HStack width={"100%"} gap={5} justifyContent={"space-between"}>
          <Input
            label="Email"
            color="background"
            placeholder="Digite o email do usuário"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("email")}
            error={errors.email}
          />
          <InputMaskIcon
            label="CPF"
            mask={"999.999.999-99"}
            color="background"
            fieldHeight="80px"
            borderRadius={20}
            placeholder="Digite o CPF do usuário"
            size={"md"}
            {...register("cpf")}
            error={errors.cpf}
          />
        </HStack>
        <InputSelect
          {...register("planSecureId")}
          color={"white"}
          control={control}
          label="Plano"
          placeholder="Selecione o Plano"
          rounded={"2xl"}
          error={errors.planSecureId as any}
          itensList={
            plans?.data.map((plan) => ({
            label: plan.name,
            value: plan.secureId,
          })) || []
        }
        />
        <HStack width={"100%"} gap={5} justifyContent={"space-between"} mt={5}>
          <Input
            type="password"
            label="Senha"
            color="background"
            placeholder="Digite a senha do usuário"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("password")}
            error={errors.password}
          />
          <Input
            type="password"
            label="Confirmação da Senha"
            color="background"
            placeholder="Digite a senha novamente"
            size={"md"}
            height="80px"
            borderRadius={20}
            {...register("confirmPassword")}
            error={errors.confirmPassword}
          />
        </HStack>
      </Flex>
      }
    />
  );
}
