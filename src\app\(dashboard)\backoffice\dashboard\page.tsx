"use client";

import CardDashboard from "@/components/cards/card-dashboard";
import { InputDateDashboard } from "@/components/global/inputs/input-date-dashboard";
import useDashboardBackoffice from "@/hook/dashboard/useDashboardBackoffice";
import { Flex, Grid, Spinner, Text } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";
import { useState } from "react";
import {
  IoCashOutline,
  IoChatbubbleOutline,
  IoDocumentTextOutline,
  IoPersonOutline,
  IoBusinessOutline,
} from "react-icons/io5";

export default function Dashboard() {
  const [startDate, setStartDate] = useState(""); // Estado para a Data Início
  const [endDate, setEndDate] = useState(""); // Estado para a Data Fim

  const { data, isLoading, isFetching } = useDashboardBackoffice(startDate, endDate);
  const isLoadingDashboard = isLoading || isFetching;

  return (
    <BackofficePageContainer>

        <Flex mb={4} justifyContent="space-between" alignItems="center" flexWrap="wrap">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Dashboard
          </Text>

          <Flex
            gap={4}
            alignItems="center"
            flexWrap="wrap"
            justifyContent={{ base: "flex-start", md: "flex-end" }}
          >
            <InputDateDashboard
              label="Data Início:"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
            <InputDateDashboard
              label="Data Fim:"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </Flex>
        </Flex>

        {isLoadingDashboard ? (
          <Flex justify="center" align="center" w="100%" h="200px">
            <Spinner color="white" size="xl" />
          </Flex>
        ) : data ? (
          <Grid
            templateColumns={{
              base: "1fr",
              sm: "repeat(2, 1fr)",
              lg: "repeat(3, 1fr)",
              xl: "repeat(3, 1fr)",
            }}
            gap={{ base: 4, md: 6 }}
            w="100%"
            autoRows="minmax(150px, auto)"
            overflow="auto"
            // maxH="calc(100vh - 200px)"
            paddingTop={2}
            css={{
              boxSizing: "border-box",
              paddingRight: "8px", 
              "&::-webkit-scrollbar": { width: "6px" },
              "&::-webkit-scrollbar-track": { background: "transparent" },
              "&::-webkit-scrollbar-thumb": {
                background: "rgba(85, 85, 85, 0.4)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb:hover": {
                background: "rgba(102, 102, 102, 0.6)",
              },
            }}

          >
            <CardDashboard
              icon={<IoBusinessOutline size={30} />}
              percentage={0}
              title="Contas"
              percentagePositive
              value={data.accounts.quantity}
            />
            <CardDashboard
              icon={<IoPersonOutline size={30} />}
              percentage={0}
              title="Atendentes"
              percentagePositive
              value={data.attendants.quantity}
            />
            <CardDashboard
              icon={<IoChatbubbleOutline size={30} />}
              percentage={0}
              title="Conversas"
              percentagePositive
              value={data.chats.quantity}
            />
            <CardDashboard
              icon={<IoDocumentTextOutline size={30} />}
              percentage={0}
              title="Assinaturas Trial"
              percentagePositive
              value={data.subscriptions.trials}
            />
            <CardDashboard
              icon={<IoDocumentTextOutline size={30} />}
              percentage={0}
              title="Assinaturas Pagas"
              percentagePositive
              value={data.subscriptions.paid}
            />
            <CardDashboard
              icon={<IoDocumentTextOutline size={30} />}
              percentage={0}
              title="Assinaturas Gratuitas"
              percentagePositive
              value={data.subscriptions.free}
            />
            <CardDashboard
              icon={<IoDocumentTextOutline size={30} />}
              percentage={0}
              title="Assinaturas Canceladas"
              percentagePositive
              value={data.subscriptions.canceled}
            />
            <CardDashboard
              icon={<IoCashOutline size={30} />}
              percentage={0}
              title="Transações Pagas"
              percentagePositive
              value={data.transactions.paidAmount.formattedValue}
            />
            <CardDashboard
              icon={<IoCashOutline size={30} />}
              percentage={700}
              title="Transações Pendentes"
              percentagePositive
              value={data.transactions.waitingAmount.formattedValue}
            />
            <CardDashboard
              icon={<IoCashOutline size={30} />}
              percentage={0}
              title="Transações Não Pagas"
              percentagePositive
              value={data.transactions.unpaidAmount.formattedValue}
            />
            <CardDashboard
              icon={<IoCashOutline size={30} />}
              percentage={0}
              title="Transações Reembolsadas"
              percentagePositive
              value={data.transactions.refundedAmount.formattedValue}
            />
          </Grid>
        ) : (
          <Flex justify="center" align="center" w="100%" h="200px">
            <Text color="white">Nenhum dado disponível para o período selecionado</Text>
          </Flex>
        )}
    </BackofficePageContainer>
  );
}
