import {
  InputProps as ChakraInputProps,
  Input as ChakraSearchInput,
} from "@chakra-ui/react";
import {
  SelectContent,
  SelectItem,
  SelectRoot,
  SelectTrigger,
  SelectValueText,
} from "@/components/ui/select";
import { forwardRef, ForwardRefRenderFunction, useState, useMemo } from "react";
import { Controller, FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";

// PASSO 1: IMPORTE O TIPO CORRETO DA SUA BIBLIOTECA DE UI
// Exemplo se estiver usando Ark UI e o tipo se chamar CollectionApi:
// import { type CollectionApi as MinhaListCollectionType } from '@ark-ui/react';
//
// Ou, se o tipo se chamar ListCollection e vier de um caminho específico:
// import { type ListCollection as MinhaListCollectionType } from 'caminho/para/seus/tipos/reais';
//
// Substitua `MinhaListCollectionType` pelo nome real do tipo e o caminho da importação.
// Se você não tem certeza, verifique a documentação da biblioteca dos seus componentes de Select
// ou a definição do componente `SelectRoot`.

// Se você não conseguir encontrar o tipo exato para importar, como último recurso
// você poderia usar `any`, mas isso removeria a segurança de tipo. Tente ao máximo encontrar o tipo correto.
// Exemplo com `any` (NÃO RECOMENDADO se puder evitar):
// type MinhaListCollectionType<T = any> = any;

// Tipo para os itens da lista
type ItemType = { label: string; value: string };

// PASSO 2: CERTIFIQUE-SE DE QUE O PLACEHOLDER DA INTERFACE ListCollection FOI REMOVIDO.

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  height?: string;
  placeholder?: string;
  control: any;
  itensList: ItemType[];
  multiple?: boolean;
  /**
   * Função obrigatória para criar a estrutura de coleção esperada pelo SelectRoot.
   * Deve ser importada da sua biblioteca de UI (ex: @ark-ui/react).
   */
  // PASSO 3: Use o tipo importado (MinhaListCollectionType) aqui
  // createListCollection: (data: { items: ItemType[] }) => MinhaListCollectionType<ItemType>; // Substitua MinhaListCollectionType
  createListCollection: (data: { items: ItemType[] }) => MinhaListCollectionType<ItemType>; // Substitua MinhaListCollectionType
}

const InputBase: ForwardRefRenderFunction<HTMLSelectElement, InputProps> = (
  {
    name,
    label,
    error,
    height,
    labelColor,
    control,
    itensList,
    placeholder,
    multiple = false,
    createListCollection,
    ...rest
  },
  ref
) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredItems = useMemo(() => {
    if (!searchTerm) {
      return itensList;
    }
    return itensList.filter((item) =>
      item.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [itensList, searchTerm]);

  // PASSO 3 (continuação): Use o tipo importado (MinhaListCollectionType) aqui também
  const collectionToUse: MinhaListCollectionType<ItemType> = useMemo(() => { // Substitua MinhaListCollectionType
    return createListCollection({ items: filteredItems });
  }, [filteredItems, createListCollection]);


  const handleSearchInputInteraction = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  return (
    <Field
      invalid={!!error}
      h={height || "auto"}
      errorText={error?.message}
      label={label}
      labelColor={labelColor}
    >
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <SelectRoot
            multiple={multiple}
            _hover={{
              borderColor: error ? "red.400" : "chatPrimary",
            }}
            _placeholder={{ color: "#B1A0A5" }}
            _focus={{
              borderColor: error ? "red.500" : "chatPrimary",
            }}
            // size="md"
            bg="white"
            color="chatPrimary"
            borderColor="#D6D6D6"
            borderWidth={2}
            borderRadius={20}
            name={field.name}
            value={field.value}
            onValueChange={(val: any) => {
              if (typeof val === 'object' && val !== null && 'value' in val && !multiple) {
                 field.onChange((val as {value: any}).value);
              } else if (typeof val === 'object' && val !== null && Array.isArray(val) && multiple) {
                field.onChange(val.map(v => typeof v === 'object' && v !== null && 'value' in v ? v.value : v));
              }
              else {
                field.onChange(val);
              }
            }}
            onInteractOutside={(e) => {
                const searchInput = (e.target as HTMLElement)?.closest('.search-input-select');
                if (searchInput) {
                    e.preventDefault();
                } else {
                    field.onBlur();
                }
            }}
            collection={collectionToUse}
            {...rest}
          >
            <SelectTrigger borderColor="#D6D6D6" clearable>
              <SelectValueText
                placeholder={placeholder}
                ref={ref}
                borderColor="#D6D6D6"
              />
            </SelectTrigger>
            <SelectContent
              bgColor={"#D6D6D6"}
              shadow={"none"}
              color={"chatPrimary"}
              _hover={{ bgColor: "#D6D6D6" }}
              zIndex={1500}
            >
              <ChakraSearchInput
                className="search-input-select"
                placeholder="Pesquisar..."
                value={searchTerm}
                onChange={handleSearchInputChange}
                onClick={handleSearchInputInteraction}
                onKeyDown={handleSearchInputInteraction}
                m={2}
                borderColor="gray.300"
                // focusBorderColor="chatPrimary"
                bg="white"
              />

              {collectionToUse && (collectionToUse as any).items && (collectionToUse as any).items.length > 0 ? (
                (collectionToUse as any).items.map((item: ItemType) => ( // Use type assertion if `items` is not directly on MinhaListCollectionType
                  <SelectItem
                    item={item}
                    key={item.value}
                    borderColor="#D6D6D6"
                    bgColor={"#D6D6D6"}
                    borderRadius={10}
                    p={3}
                    _hover={{ bgColor: "#fff" }}
                  >
                    {item.label}
                  </SelectItem>
                ))
              ) : (
                <div style={{ padding: '10px', textAlign: 'center', color: '#333' }}>
                  {searchTerm ? "Nenhum resultado encontrado." : "Nenhum item."}
                </div>
              )}
            </SelectContent>
          </SelectRoot>
        )}
      />
    </Field>
  );
};

export const InputSelect = forwardRef(InputBase);

// Exemplo de como você chamaria, passando a função e importando o tipo:
//
// import { collection as createListCollection, type CollectionApi as MinhaListCollectionType } from '@ark-ui/react';
// // Lembre-se que MinhaListCollectionType deve ser usado na definição do InputSelect.tsx como mostrado acima.
//
// <InputSelect
//   control={control}
//   {...register("contactSecureId")}
//   label="Quem você deseja contactar?"
//   placeholder="Selecione o contato"
//   labelColor={"chatTextColor"}
//   itensList={contactList || []}
//   createListCollection={createListCollection}
// />
