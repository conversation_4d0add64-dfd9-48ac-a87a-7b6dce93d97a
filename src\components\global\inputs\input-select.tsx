import {
  InputProps as ChakraInputProps,
  createListCollection,
} from "@chakra-ui/react";
import {
  SelectContent,
  SelectItem,
  SelectRoot,
  SelectTrigger,
  SelectValueText,
} from "@/components/ui/select";
import { forwardRef, ForwardRefRenderFunction } from "react";
import { Controller, FieldError } from "react-hook-form";
import { Field } from "@/components/ui/field";
import { InputGroup } from "@/components/ui/input-group";

interface InputProps extends ChakraInputProps {
  name: string;
  label?: string;
  labelColor?: string;
  error?: FieldError;
  comments?: string;
  startElement?: React.ReactNode;
  endElement?: React.ReactNode;
  height?: string;
  placeholder?: string;
  control: any;
  itensList: { label: string; value: string }[];
  multiple?: boolean;
}

const InputBase: ForwardRefRenderFunction<HTMLSelectElement, InputProps> = (
  {
    name,
    label,
    error,
    startElement,
    endElement,
    height,
    labelColor,
    control,
    itensList,
    placeholder,
    multiple = false,
    ...rest
  },
  ref
) => {
  const frameworks = createListCollection({
    items: itensList,
  });
  return (
    <Field
      invalid={!!error}
      h={"60px"}
      errorText={error?.message}
      label={label}
      labelColor={labelColor}
    >
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <SelectRoot
            multiple={multiple}
            _hover={{
              borderColor: error ? "red.400" : "chatPrimary",
            }}
            _placeholder={{ color: "#B1A0A5" }}
            _focus={{
              borderColor: error ? "red.500" : "chatPrimary",
            }}
            size="md"
            bg="white"
            color="chatPrimary"
            borderColor="#D6D6D6"
            borderWidth={2}
            borderRadius={20}
            name={field.name}
            value={field.value}
            onValueChange={({ value }) => field.onChange(value)}
            onInteractOutside={() => field.onBlur()}
            collection={frameworks}
          >
            <SelectTrigger borderColor="#D6D6D6" clearable>
              <SelectValueText
                placeholder={placeholder}
                ref={ref}
                borderColor="#D6D6D6"
              />
            </SelectTrigger>
            <SelectContent
              bgColor={"#D6D6D6"}
              shadow={"none"}
              color={"chatPrimary"}
              _hover={{ bgColor: "#D6D6D6" }}
              zIndex={1500}
            >
              {frameworks.items.map((movie) => (
                <SelectItem
                  item={movie}
                  key={movie.value}
                  borderColor="#D6D6D6"
                  bgColor={"#D6D6D6"}
                  borderRadius={10}
                  p={3}
                  _hover={{ bgColor: "#fff" }}
                >
                  {movie.label}
                </SelectItem>
              ))}
            </SelectContent>
          </SelectRoot>
        )}
      />
    </Field>
  );
};

export const InputSelect = forwardRef(InputBase);
