import { <PERSON>u<PERSON><PERSON><PERSON>, <PERSON>uRoot, MenuTrigger } from "@/components/ui/menu";
import useNotification from "@/hook/notification/useNotification";
import {
  AbsoluteCenter,
  HStack,
  Spinner,
  Text,
  VStack,
  Box,
} from "@chakra-ui/react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { FaBell } from "react-icons/fa";
import { useInView } from "react-intersection-observer";
import { useEffect } from "react";
import { api } from "@/services/api";

export default function Notifications() {
  const { 
    data, 
    isLoading, 
    isError,
    fetchNextPage,
    hasNextPage,
    refetch,
    isFetchingNextPage
  } = useNotification();
  const route = useRouter();

  const { ref: loadMoreRef, inView } = useInView();

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const allNotifications = data?.pages.flatMap((page) => page.data) ?? [];

  const handleReadSessionNotification = async (sessionSecureId: string, notificationSecureId: string ) => {
    api.get(`notifications/read/${notificationSecureId}`)
      .then(() => {
        refetch();
      })
    route.push(`/app/chat/${sessionSecureId}`)
  }

  return (
    <MenuRoot>
      <MenuTrigger _hover={{ cursor: "pointer" }} outline={"none"}>
        <Box position={"relative"}>
          {allNotifications && allNotifications.length > 0 && (
            <Box
              w="8px"
              h="8px"
              borderRadius="50%"
              bg="chatPrimary"
              top={-1}
              right={0}
              position={"absolute"}
            />
          )}
          <FaBell
            color="#84767A"
            size={16}
            style={{ marginRight: "8px", cursor: "pointer" }}
          />
        </Box>
      </MenuTrigger>
      <MenuContent
        bgColor={"chatCardBackground"}
        outline={"none"}
        shadow={"md"}
        maxH="200px"
        overflowY="auto"
        css={{
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-track": {
                width: "6px",
                marginTop: "10px",
                marginBottom: "10px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#D6D6D6",
                borderRadius: "24px",
                "&:hover": {
                  background: "#A6A6A6",
                },
              },
            }}
      >
        <VStack >
          {isLoading && (
            <AbsoluteCenter>
              <Spinner color="chatPrimary" />
            </AbsoluteCenter>
          )}
          {allNotifications && allNotifications.length > 0 ? (
            <>
              {allNotifications.map((notification) => (
                <HStack
                  key={notification.secureId}
                  transition="all 0.2s ease-in-out"
                  _hover={{
                    cursor: "pointer",
                    backgroundColor: "rgba(94, 94, 94, 0.1)",
                    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                  }}
                  onClick={() => handleReadSessionNotification(notification.sessionSecureId, notification.secureId)}
                  padding={2}
                  width="100%"
                  alignItems="center"
                  justifyContent="flex-start"
                >
                  <Box
                    w="8px"
                    h="8px"
                    borderRadius="50%"
                    bg="chatPrimary"
                    marginTop="6px"
                    marginRight="2px"
                  />
                  <VStack align="flex-start" gap={0}>
                    <Text color={"chatTextColor"}>
                      {notification.title} - {format(notification.createdAt, "dd/MM/yy HH:mm")}
                    </Text>
                    {notification.createdAt && (
                      <Text color={"chatTextColor"}>
                        {notification.message}
                      </Text>
                    )}
                  </VStack>
                </HStack>
              ))}
              {hasNextPage && (
                <Box ref={loadMoreRef} h="1px" />
              )}
              {isFetchingNextPage && (
                <Spinner color="chatPrimary" size="sm" />
              )}
            </>
          ) : (
            <Box>
              <Text color='chatTextColor'>Sem Notificações</Text>
            </Box>
          )}
        </VStack>
      </MenuContent>
    </MenuRoot>
  );
}
