"use client";
import { Flex, Grid, GridItem, Text, VStack } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";


export default function BackofficeChatbots() {
  return (
    <>
      <BackofficePageContainer>
        <Flex direction="column" gap={4} w="100%">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Chatbots
          </Text>
          <Text fontSize="md" color="gray.400">
            Total de chatbots: 10
          </Text>
          <Grid
            templateColumns={{ base: "1fr", md: `repeat(6, 1fr)` }}
            gap={4}
            width="100%"
            px={5}
            py={3}
            color={"chatPrimary"}
            rounded={"xl"}
            fontWeight="bold"
            display={{ base: "none", md: "grid" }}
          >
            <GridItem>
              <Text textAlign={"center"}>Nome</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Conta</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Tokens entrada</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Token saída</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Mensagens Enviadas</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Sessões respondidas</Text>
            </GridItem>
          </Grid>
          <VStack gap={3} width="100%">
            {chatbots &&
              chatbots.data.length > 0 &&
              chatbots.data.map((chatbot) => (
                
                />
              ))}
          </VStack>
        </Flex>
      </BackofficePageContainer>
    </>
  );
}