"use client";
import { Flex, Grid, GridItem, Text, VStack, Center, Spinner } from "@chakra-ui/react";
import BackofficePageContainer from "../components/backoffice-page-container";
import useBackofficeChatbots from "@/hook/backoffice/useBackofficeChatbots";
import ChatbotCard from "./components/chatbot-card";
import { GetBackofficeChatbotsDto } from "@/utils/types/DTO/backoffice-chatbots.dto";

export default function BackofficeChatbots() {
  const { data: chatbots, isLoading, error } = useBackofficeChatbots();
  console.log(chatbots);

  if (isLoading) {
    return (
      <BackofficePageContainer>
        <Center flex={1}>
          <Spinner size="xl" color="white" />
        </Center>
      </BackofficePageContainer>
    );
  }

  if (error) {
    return (
      <BackofficePageContainer>
        <Center flex={1}>
          <Text color="red.400" fontSize="lg">
            Erro ao carregar chatbots
          </Text>
        </Center>
      </BackofficePageContainer>
    );
  }

  return (
    <>
      <BackofficePageContainer>
        <Flex direction="column" gap={4} w="100%">
          <Text
            fontSize={{ base: "xl", md: "2xl" }}
            fontWeight="bold"
            color="white"
            mb={{ base: 4, md: 0 }}
          >
            Chatbots
          </Text>
          <Text fontSize="md" color="gray.400">
            {/* Total de chatbots: {chatbots?.meta.totalItems || 0} */}
          </Text>

          {/* Header da tabela - apenas desktop */}
          <Grid
            templateColumns={{ base: "1fr", md: `repeat(6, 1fr)` }}
            gap={4}
            width="100%"
            px={5}
            color={"white"}
            rounded={"xl"}
            fontWeight="bold"
            display={{ base: "none", md: "grid" }}
          >
            <GridItem>
              <Text textAlign={"center"}>Nome</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Conta</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Tokens entrada</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Tokens saída</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Mensagens Enviadas</Text>
            </GridItem>
            <GridItem>
              <Text textAlign={"center"}>Sessões respondidas</Text>
            </GridItem>
          </Grid>

          {/* Lista de chatbots */}
          <VStack gap={3} width="100%">
            {chatbots && chatbots.data.length > 0 ? (
              chatbots.data.map((chatbot) => (
                <ChatbotCard key={chatbot.secureId} chatbot={chatbot} />
              ))
            ) : (
              <Center py={10}>
                <Text color="gray.400" fontSize="lg">
                  Nenhum chatbot encontrado
                </Text>
              </Center>
            )}
          </VStack>
        </Flex>
      </BackofficePageContainer>
    </>
  );
}