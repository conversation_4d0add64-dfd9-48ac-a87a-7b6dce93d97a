import {
  <PERSON>,
  <PERSON>,
  Flex,
  <PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  Text,
  VStack,
  useBreakpointValue,
} from "@chakra-ui/react";
import { LuCircleHelp } from "react-icons/lu";
import HeaderTab from "../header-tab";
import NumbersItens from "./number-item";
import useWhatsappIntegrations from "@/hook/config/useWhatsappIntegrations";
import BasicModal from "@/components/global/modal/basic-modal";
import { useState } from "react";
import { Input } from "@/components/global/inputs/input";
import { IoPhonePortraitOutline } from "react-icons/io5";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { InputMaskIcon } from "@/components/global/inputs/input-mask-icon";
import { useMutation } from "@tanstack/react-query";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { SwitchLabel } from "@/components/global/inputs/switch";
import { Tooltip } from "@/components/ui/tooltip";
import { ResponseCreateWppBussinesIntegrationDto, ResponseCreateWppIntegrationDto } from "@/utils/types/DTO/wpp-integration.dto";

const createWppIntegrationSchema = yup.object().shape({
  cellPhone: yup
    .string()
    .required("Telefone é obrigatório")
    .test("is-valid-phone", "Telefone inválido", (value) => {
      if (!value) return false;
      const onlyNumbers = value.replace(/\D/g, "");
      return onlyNumbers.length === 13 || onlyNumbers.length === 12;
    }),
  isBusiness: yup.boolean().required("Tipo de Número é obrigatório"),
  businessId: yup.string().when("isBusiness", (isBusiness, schema) => {
    if (isBusiness[0]) {
      return schema.required("Id do Negócio é obrigatório");
    }
    return schema.notRequired();
  }),
  numberId: yup.string().when("isBusiness", (isBusiness, schema) => {
    if (isBusiness[0]) {
      return schema.required("Id do Número é obrigatório");
    }
    return schema.notRequired();
  }),
  token: yup.string().when("isBusiness", (isBusiness, schema) => {
    if (isBusiness[0]) {
      return schema.required("Token é obrigatório");
    }
    return schema.notRequired();
  }),
});

type CreateWppIntegrationFormData = yup.InferType<
  typeof createWppIntegrationSchema
>;

export default function WhatsAppTab() {
  const { data, isLoading, isFetching } = useWhatsappIntegrations({});
  const [openModalCreate, setOpenModalCreate] = useState(false);
  const [openModalQrCodeOrWebhook, setOpenModalQrCodeOrWebhook] =
    useState(false);
  const [isBusiness, setIsBusiness] = useState(false);

  // Responsive breakpoint
  const isMobile = useBreakpointValue({ base: true, md: false });

  // QRCode and Pairing Code
  const [QRCode, setQRCode] = useState<string | undefined>(undefined);
  const [pairingCode, setPairingCode] = useState<string | undefined>(undefined);

  //Webhook business
  const [webhookBusiness, setWebhookBusiness] = useState("");
  const [accessToken, setAccessToken] = useState("");

  const phoneMask = "+99 (99) 99999-9999[9]";

  const {
    register,
    handleSubmit,
    formState,
    watch,
    setValue,
    resetField,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CreateWppIntegrationFormData>({
    resolver: yupResolver(createWppIntegrationSchema),
    defaultValues: {
      isBusiness: false,
    },
  });

  const handleCreate = () => {
    handleReset();
    setOpenModalCreate(true);
  };

  const onSubmit = async (data: CreateWppIntegrationFormData) => {
    try {
      await createWppIntegration.mutateAsync(data);
    } catch (e) { }
  };

  const createWppIntegration = useMutation({
    mutationFn: async (data: CreateWppIntegrationFormData) => {
      if (isBusiness) {
        const webhookPublicMeta = process.env.NEXT_PUBLIC_API_META ?? "https://api.plyrchat.com.br/meta/webhook";
        const { data: responseData } = await api.post<ResponseCreateWppBussinesIntegrationDto>("/whatsapp-integration", {
          phoneNumber: data.cellPhone,
          businessId: data.businessId,
          numberId: data.numberId,
          token: data.token,
        })

        const webhookUrl = `${webhookPublicMeta}?secureId=${responseData.secureId}`;
        setWebhookBusiness(webhookUrl);
        setAccessToken(responseData.webhookToken);
      } else {
        const { data: responseData } =
          await api.post<ResponseCreateWppIntegrationDto>(
            "/whatsapp/integration",
            {
              phoneNumber: data.cellPhone,
              isBusiness: isBusiness,
              businessId: data.businessId,
              numberId: data.numberId,
              token: data.token,
            }
          );
        setQRCode(responseData.qrcodeBase64);
        setPairingCode(responseData.pairingCode);
      }

      setOpenModalCreate(false);
      setOpenModalQrCodeOrWebhook(true);
    },
    onError: (error) => { },
    onSuccess: () => {
      toaster.success({
        title: "Número de Whatsapp cadastrado com sucesso, leia o QRCode",
      });
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-integrations"],
      });
    },
  });

  const handleReset = () => {
    resetField("cellPhone");
    resetField("isBusiness");
    resetField("businessId");
    resetField("numberId");
    resetField("token");
  };

  return (
    <>
      <Flex
        h={"100%"}
        bgColor={"chatCardBackground"}
        p={"5"}
        rounded={"2xl"}
        border={"none"}
      >
        {!data || isLoading || isFetching ? (
          <Center flex={1}>
            <Spinner color={"chatPrimary"} />
          </Center>
        ) : (
          <VStack flex={1} alignItems={"flex-start"} mt={{ base: 8, md: 0 }}>
            <HeaderTab
              title="Whatsapp"
              buttonTitle={!isMobile ? "Adicionar Número" : undefined}
              onClick={!isMobile ? handleCreate : undefined}
            />
            {isMobile ? (
              <VStack
                flex={1}
                w={"100%"}
                alignItems={"stretch"}
                gap={3}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum Número Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data.data.map((item) => (
                      <NumbersItens
                        key={item.secureId}
                        secureId={item.secureId}
                        number={item.phoneNumber}
                        chatBotSecureId={item.chatBotSecureId}
                        isBusiness={item.isBusiness}
                      />
                    ))}
                  </>
                )}
              </VStack>
            ) : (
              <HStack
                flex={1}
                flexWrap={"wrap"}
                w={"100%"}
                alignContent={"start"}
                mt={5}
              >
                {data.data.length === 0 ? (
                  <Flex width={"100%"} color={"chatTextColor"}>
                    Nenhum Número Cadastrado
                  </Flex>
                ) : (
                  <>
                    {data.data.map((item) => (
                      <NumbersItens
                        key={item.secureId}
                        secureId={item.secureId}
                        number={item.phoneNumber}
                        chatBotSecureId={item.chatBotSecureId}
                        isBusiness={item.isBusiness}
                      />
                    ))}
                  </>
                )}
              </HStack>
            )}
          </VStack>
        )}
      </Flex>
      <BasicModal
        open={openModalCreate}
        setOpen={setOpenModalCreate}
        asForm
        cancelText="Cancelar"
        confirmText="Adicionar"
        title="Adicionar Número de Whatsapp"
        placement="center"
        isSubmitting={isSubmitting}
        handleSubmit={handleSubmit(onSubmit)}
        children={
          <Flex flex={1} flexDir={"column"} alignItems={"flex-end"} m={3}>
            <SwitchLabel
              control={control}
              label="Whatsapp Comercial"
              checkedValue={isBusiness}
              {...register("isBusiness")}
              onCheckedValue={setIsBusiness}
              onChange={() => {
                resetField("businessId");
                resetField("numberId");
                resetField("token");
              }}
            />
            {isBusiness ? (
              <>
                <InputMaskIcon
                  mask={phoneMask}
                  startElement={<IoPhonePortraitOutline />}
                  borderRadius={20}
                  placeholder="Digite seu Telefone"
                  size={"md"}
                  {...register("cellPhone")}
                  error={errors.cellPhone}
                />
                <HStack mb={6}>
                  <Input
                    placeholder="Digite o Business ID"
                    size={"md"}
                    height="80px"
                    label="Identificação do Negócio (Business ID)"
                    borderRadius={20}
                    endElement={
                      <Tooltip content="ID do seu negócio na plataforma da Meta">
                        <LuCircleHelp />
                      </Tooltip>
                    }
                    {...register("businessId")}
                    error={errors.businessId}
                  />
                  <Input
                    placeholder="Digite o Number ID"
                    size={"md"}
                    height="80px"
                    label="Identificação do Número (Number ID)"
                    borderRadius={20}
                    endElement={
                      <Tooltip content="ID associado ao número de telefone integrado">
                        <LuCircleHelp />
                      </Tooltip>
                    }
                    {...register("numberId")}
                    error={errors.numberId}
                  />
                </HStack>
                <Input
                  placeholder="Digite o Token de Autenticação"
                  size={"md"}
                  height="80px"
                  label="Token de Autenticação"
                  borderRadius={20}
                  endElement={
                    <Tooltip content="Token gerado para acessar a API da Meta">
                      <LuCircleHelp />
                    </Tooltip>
                  }
                  {...register("token")}
                  error={errors.token}
                />
              </>
            ) : (
              <InputMaskIcon
                mask={phoneMask}
                startElement={<IoPhonePortraitOutline />}
                borderRadius={20}
                placeholder="Digite seu Telefone"
                size={"md"}
                {...register("cellPhone")}
                error={errors.cellPhone}
              />
            )}
          </Flex>
        }
      />
      <BasicModal
        open={openModalQrCodeOrWebhook}
        setOpen={setOpenModalQrCodeOrWebhook}
        cancelText="Fechar"
        title="Leia o QRCode"
        placement="center"
        handleConfirm={() => {
          setOpenModalQrCodeOrWebhook(false);
        }}
        children={
          <Flex flex={1} flexDir={"column"} alignItems={"center"}>
            {isBusiness ? (
              <>
                <Text fontWeight={"medium"} pt={5} pb={1}>
                  WebHook para uso na plataforma meta
                </Text>
                <Text
                  fontSize={"md"}
                  fontWeight={"bold"}
                  _hover={{
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    navigator.clipboard.writeText(webhookBusiness);
                    toaster.create({
                      title: "Webhook copiado",
                      description: "Webhook copiado com sucesso",
                      type: "success",
                    });
                  }}
                >
                  {webhookBusiness}
                </Text>
                <Text fontWeight={"medium"} pt={5} pb={1}>
                  Token de Autenticação para uso na plataforma meta
                </Text>
                <Text
                  fontSize={"md"}
                  fontWeight={"bold"}
                  _hover={{
                    cursor: "pointer",
                  }}
                  wordBreak={"break-all"}
                  textAlign={"center"}
                  onClick={() => {
                    navigator.clipboard.writeText(accessToken);
                    toaster.create({
                      title: "Token copiado",
                      description: "Token copiado com sucesso",
                      type: "success",
                    });
                  }}
                >
                  {accessToken}
                </Text>
              </>
            ) : (
              <>
                <Box>
                  <img src={QRCode} alt="QRCode" />
                </Box>
                <Text fontWeight={"medium"} pt={5} pb={1}>
                  Código de Pareamento
                </Text>
                <Text fontSize={"md"} fontWeight={"bold"}>
                  {pairingCode}
                </Text>
              </>
            )}
          </Flex>
        }
      />
    </>
  );
}
