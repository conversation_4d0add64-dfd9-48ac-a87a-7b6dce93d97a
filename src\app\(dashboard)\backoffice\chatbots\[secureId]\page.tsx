"use client";
import {
  <PERSON><PERSON>,
  VStack,
  Text,
  Center,
  Spinner,
  Button,
  HStack,
  useBreakpointValue,
} from "@chakra-ui/react";
import { useParams, useRouter } from "next/navigation";
import BackofficePageContainer from "../../components/backoffice-page-container";
import useBackofficeChatbotDetail from "@/hook/backoffice/useBackofficeChatbotDetail";
import StatisticsOverview from "./components/statistics-overview";
import ConfigurationDisplay from "./components/configuration-display";
import MessageHistory from "./components/message-history";
import KnowledgeBaseDisplay from "./components/knowledge-base-display";
import { LuArrowLeft, LuRefreshCw } from "react-icons/lu";

export default function BackofficeChatbotDetail() {
  const params = useParams();
  const router = useRouter();
  const secureId = params.secureId as string;
  const isMobile = useBreakpointValue({ base: true, md: false });

  const { data: chatbot, isLoading, error, refetch, isFetching } = useBackofficeChatbotDetail(secureId);
  console.log(chatbot);

  if (isLoading) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Spinner size="xl" color="white" />
            <Text color="white" mt={4}>
              Carregando detalhes do chatbot...
            </Text>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  if (error) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Text color="red.400" fontSize="lg" textAlign="center">
              Erro ao carregar detalhes do chatbot
            </Text>
            <Text color="gray.400" fontSize="sm" textAlign="center">
              Verifique se o ID do chatbot está correto
            </Text>
            <Button
              mt={4}
              colorScheme="red"
              variant="outline"
              onClick={() => router.push("/backoffice/chatbots")}
            >
              Voltar para lista
            </Button>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  if (!chatbot) {
    return (
      <BackofficePageContainer>
        <Center h="400px">
          <VStack>
            <Text color="gray.400" fontSize="lg">
              Chatbot não encontrado
            </Text>
            <Button
              mt={4}
              colorScheme="purple"
              variant="outline"
              onClick={() => router.push("/backoffice/chatbots")}
            >
              Voltar para lista
            </Button>
          </VStack>
        </Center>
      </BackofficePageContainer>
    );
  }

  return (
    <BackofficePageContainer>
      <Flex direction="column" gap={6} w="100%">
        {/* Header */}
        <Flex
          direction={{ base: "column", md: "row" }}
          justify="space-between"
          align={{ base: "start", md: "center" }}
          gap={4}
        >
          <VStack align="start" gap={2}>
            <HStack>
              <Button
                size="sm"
                variant="ghost"
                color="white"
                onClick={() => router.push("/backoffice/chatbots")}
              >
                <LuArrowLeft />
                Voltar
              </Button>
            </HStack>
            <Text
              fontSize={{ base: "xl", md: "2xl" }}
              fontWeight="bold"
              color="white"
            >
              Detalhes do Chatbot
            </Text>
            <Text fontSize="md" color="gray.400">
              Visualização completa das configurações e estatísticas
            </Text>
          </VStack>

          <Button
            size="sm"
            colorScheme="purple"
            variant="outline"
            onClick={() => refetch()}
            disabled={isFetching}
          >
            <LuRefreshCw />
            {isFetching ? "Atualizando..." : "Atualizar"}
          </Button>
        </Flex>

        {/* Content */}
        <VStack gap={6} align="stretch">
          {/* Statistics Overview */}
          <StatisticsOverview chatbot={chatbot} />

          {/* Configuration Display */}
          <ConfigurationDisplay chatbot={chatbot} />

          {/* Knowledge Base */}
          <KnowledgeBaseDisplay chatbot={chatbot} />

          {/* Message History */}
          <MessageHistory chatbot={chatbot} isLoading={isLoading} />
        </VStack>
      </Flex>
    </BackofficePageContainer>
  );
}
