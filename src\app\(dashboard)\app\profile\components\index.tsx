"use client";
import { Tabs } from "@chakra-ui/react";
import { HiOutlinePencilSquare } from "react-icons/hi2"
import ChatBotTab from "../../config/components/tabs/chatbot-tab/indext";
import { TabTrigger } from "../../config/components/tabs/components/tab-trigger";
import AppProfileTab from "./tabs/profile-tab";
import AppSubscriptionTab from "./tabs/subscription-tab";
import { IoCardOutline, IoPersonCircleOutline } from "react-icons/io5";
import { useGetProfile } from "@/hook/profile/useGetProfile";
import { useEffect, useState } from "react";

export default function ProfileTabs() {
  const { data: profileData } = useGetProfile();

    const [activeTab, setActiveTab] = useState<string | undefined>(undefined);
  
    // Function to get tab value from URL hash
    const getTabFromHash = () => {
      if (typeof window !== "undefined") {
        const hash = window.location.hash.replace("#", "");
        return hash || "my-account"; // Default to whatsapp if no hash
      }
      return "my-account";
    };
  
    // Set active tab based on URL hash when component mounts
    useEffect(() => {
      const tabFromHash = getTabFromHash();
      setActiveTab(tabFromHash);
    }, []);
  
    // Listen for hash changes
    useEffect(() => {
      const handleHashChange = () => {
        const tabFromHash = getTabFromHash();
        setActiveTab(tabFromHash);
      };
  
      window.addEventListener("hashchange", handleHashChange);
      return () => {
        window.removeEventListener("hashchange", handleHashChange);
      };
    }, []);
  
    // Handle tab change
    const handleTabChange = (details: { value: string }) => {
      setActiveTab(details.value);
      // Update URL hash
      if (typeof window !== "undefined") {
        window.location.hash = details.value;
      }
    };

  return (
    <Tabs.Root
     value={activeTab}
      onValueChange={handleTabChange}
      activationMode="manual"
      orientation="vertical"
      gap={1}
      flex={1}
      size={"lg"}
      variant="enclosed"
    >
      <Tabs.List
        bgColor={"chatCardBackground"}
        p={"5"}
        width={{ base: "25%", "2xl": "15%" }}
        rounded={"2xl"}
        border={"none"}
        alignItems={"flex-start"}
      >
        <TabTrigger
          icon={<IoPersonCircleOutline />}
          label={"Minha conta"}
          value={"my-account"}
          key={"my-account"}
        />
        <TabTrigger
          icon={<HiOutlinePencilSquare />}
          label={"Assinatura"}
          value={"subscription"}
          key={"subscription"}
        />
      </Tabs.List>

      <Tabs.Content value="my-account" width={"100%"}>
      {profileData?.user && (
        <AppProfileTab user={profileData.user} />
      )}
      </Tabs.Content>
      <Tabs.Content value="subscription" width={"100%"}>
      {profileData && (
        <AppSubscriptionTab
          subscription={profileData.subscription}
          transactions={profileData.transactions}
        />
      )}
      </Tabs.Content>
    </Tabs.Root>
  );
}
