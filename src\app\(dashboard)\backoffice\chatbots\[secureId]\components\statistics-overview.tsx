import {
  Box,
  Grid,
  GridItem,
  Text,
  VStack,
  HStack,
  Badge,
} from "@chakra-ui/react";
import { StatRoot, StatLabel, StatValueText, StatHelpText } from "@/components/ui/stat";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuBot, LuBrain, LuCalendar, LuBuilding2 } from "react-icons/lu";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

type StatisticsOverviewProps = {
  chatbot: BackofficeChatbotDetailDto;
};

export default function StatisticsOverview({ chatbot }: StatisticsOverviewProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toLocaleString();
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  return (
    <Box
      bg="white"
      borderRadius="xl"
      p={6}
      shadow="sm"
      border="1px solid"
      borderColor="gray.200"
    >
      <VStack align="stretch" gap={6}>
        {/* Header with chatbot info */}
        <HStack justify="space-between" align="center">
          <HStack>
            {chatbot.isAI ? (
              <LuBrain size={24} color="#6B46C1" />
            ) : (
              <LuBot size={24} color="#059669" />
            )}
            <VStack align="start" gap={0}>
              <Text fontSize="xl" fontWeight="bold" color="gray.800">
                {chatbot.name}
              </Text>
              <Text fontSize="sm" color="gray.600">
                {chatbot.isAI ? "Chatbot com IA" : "Chatbot Simples"}
              </Text>
            </VStack>
          </HStack>
          <Badge
            colorScheme={chatbot.isActive ? "green" : "red"}
            variant="subtle"
            fontSize="sm"
            px={3}
            py={1}
          >
            {chatbot.isActive ? "Ativo" : "Inativo"}
          </Badge>
        </HStack>

        {/* Account info */}
        <HStack>
          <LuBuilding2 size={20} color="#6B7280" />
          <VStack align="start" gap={0}>
            <Text fontSize="sm" color="gray.600" fontWeight="medium">
              Conta Proprietária:
            </Text>
            <Text fontSize="md" color="gray.800" fontWeight="semibold">
              {chatbot.account.companyName}
            </Text>
          </VStack>
        </HStack>

        {/* Statistics Grid */}
        <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }} gap={4}>
          <GridItem>
            <StatRoot>
              <StatLabel color="gray.600" fontSize="sm">
                Tokens de Entrada
              </StatLabel>
              <StatValueText color="blue.600" fontSize="2xl">
                {formatNumber(chatbot.statistics.inputTokenCount)}
              </StatValueText>
              <StatHelpText color="gray.500" fontSize="xs">
                Total processado
              </StatHelpText>
            </StatRoot>
          </GridItem>

          <GridItem>
            <StatRoot>
              <StatLabel color="gray.600" fontSize="sm">
                Tokens de Saída
              </StatLabel>
              <StatValueText color="purple.600" fontSize="2xl">
                {formatNumber(chatbot.statistics.outputTokenCount)}
              </StatValueText>
              <StatHelpText color="gray.500" fontSize="xs">
                Total gerado
              </StatHelpText>
            </StatRoot>
          </GridItem>

          <GridItem>
            <StatRoot>
              <StatLabel color="gray.600" fontSize="sm">
                Mensagens Enviadas
              </StatLabel>
              <StatValueText color="green.600" fontSize="2xl">
                {formatNumber(chatbot.statistics.totalMessagesSent)}
              </StatValueText>
              <StatHelpText color="gray.500" fontSize="xs">
                Total de mensagens
              </StatHelpText>
            </StatRoot>
          </GridItem>

          <GridItem>
            <StatRoot>
              <StatLabel color="gray.600" fontSize="sm">
                Sessões Respondidas
              </StatLabel>
              <StatValueText color="orange.600" fontSize="2xl">
                {formatNumber(chatbot.statistics.totalSessionsResponded)}
              </StatValueText>
              <StatHelpText color="gray.500" fontSize="xs">
                Total de sessões
              </StatHelpText>
            </StatRoot>
          </GridItem>
        </Grid>

        {/* Dates */}
        <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
          <HStack>
            <LuCalendar size={16} color="#6B7280" />
            <VStack align="start" gap={0}>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Criado em:
              </Text>
              <Text fontSize="sm" color="gray.700">
                {formatDate(chatbot.createdAt)}
              </Text>
            </VStack>
          </HStack>

          <HStack>
            <LuCalendar size={16} color="#6B7280" />
            <VStack align="start" gap={0}>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Última atualização:
              </Text>
              <Text fontSize="sm" color="gray.700">
                {formatDate(chatbot.updatedAt)}
              </Text>
            </VStack>
          </HStack>
        </Grid>
      </VStack>
    </Box>
  );
}
