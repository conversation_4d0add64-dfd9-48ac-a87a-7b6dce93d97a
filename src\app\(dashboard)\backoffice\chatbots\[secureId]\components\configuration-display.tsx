import {
  <PERSON>,
  Grid,
  <PERSON>ridI<PERSON>,
  Text,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Separator,
  Code,
  Textarea,
} from "@chakra-ui/react";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuSettings, LuMessageSquare, LuUsers, LuThermometer } from "react-icons/lu";

type ConfigurationDisplayProps = {
  chatbot: BackofficeChatbotDetailDto;
};

export default function ConfigurationDisplay({ chatbot }: ConfigurationDisplayProps) {
  const getEmotionalToneLabel = (tone: string) => {
    const tones: Record<string, string> = {
      friendly: "Amigável",
      professional: "Profissional",
      casual: "Casual",
      enthusiastic: "Entusiasmado",
      formal: "Formal",
    };
    return tones[tone] || tone;
  };

  const getMoodLabel = (mood: string) => {
    const moods: Record<string, string> = {
      helpful: "Prestativo",
      neutral: "Neutro",
      positive: "Positivo",
      empathetic: "Empá<PERSON><PERSON>",
    };
    return moods[mood] || mood;
  };

  const getResponseStyleLabel = (style: string) => {
    const styles: Record<string, string> = {
      concise: "Conciso",
      detailed: "Detalhado",
      conversational: "Conversacional",
      technical: "Técnico",
    };
    return styles[style] || style;
  };

  const getResponseSizeLabel = (size: string) => {
    const sizes: Record<string, string> = {
      short: "Curto",
      medium: "Médio",
      long: "Longo",
    };
    return sizes[size] || size;
  };

  return (
    <Box
      bg="white"
      borderRadius="xl"
      p={6}
      shadow="sm"
      border="1px solid"
      borderColor="gray.200"
    >
      <VStack align="stretch" gap={6}>
        {/* Header */}
        <HStack>
          <LuSettings size={24} color="#6B46C1" />
          <Text fontSize="lg" fontWeight="bold" color="gray.800">
            Configurações do Chatbot
          </Text>
        </HStack>

        <Separator />

        {/* AI Configuration */}
        <VStack align="stretch" gap={4}>
          <Text fontSize="md" fontWeight="semibold" color="gray.700">
            Configurações de IA
          </Text>
          
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Tom Emocional:
                </Text>
                <Badge colorScheme="blue" variant="subtle" fontSize="sm">
                  {getEmotionalToneLabel(chatbot.emotionalTone)}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Humor:
                </Text>
                <Badge colorScheme="green" variant="subtle" fontSize="sm">
                  {getMoodLabel(chatbot.mood)}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Estilo de Resposta:
                </Text>
                <Badge colorScheme="purple" variant="subtle" fontSize="sm">
                  {getResponseStyleLabel(chatbot.responseStyle)}
                </Badge>
              </VStack>
            </GridItem>

            <GridItem>
              <VStack align="start" gap={2}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Tamanho da Resposta:
                </Text>
                <Badge colorScheme="orange" variant="subtle" fontSize="sm">
                  {getResponseSizeLabel(chatbot.responseSize)}
                </Badge>
              </VStack>
            </GridItem>
          </Grid>

          {/* Temperature */}
          <HStack>
            <LuThermometer size={16} color="#6B7280" />
            <VStack align="start" gap={0}>
              <Text fontSize="sm" color="gray.600" fontWeight="medium">
                Temperatura (Criatividade):
              </Text>
              <Text fontSize="md" color="gray.800" fontWeight="semibold">
                {(chatbot.temperature / 10).toFixed(1)} / 1.0
              </Text>
            </VStack>
          </HStack>
        </VStack>

        <Separator />

        {/* Greeting Message */}
        <VStack align="stretch" gap={3}>
          <HStack>
            <LuMessageSquare size={20} color="#6B46C1" />
            <Text fontSize="md" fontWeight="semibold" color="gray.700">
              Mensagem de Saudação
            </Text>
          </HStack>
          <Box
            bg="gray.50"
            p={4}
            borderRadius="md"
            border="1px solid"
            borderColor="gray.200"
          >
            <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
              {chatbot.greetingMessage || "Nenhuma mensagem de saudação configurada"}
            </Text>
          </Box>
        </VStack>

        {/* AI Prompt */}
        {chatbot.account.prompt && (
          <>
            <Separator />
            <VStack align="stretch" gap={3}>
              <Text fontSize="md" fontWeight="semibold" color="gray.700">
                Prompt da IA
              </Text>
              <Textarea
                value={chatbot.account.prompt}
                readOnly
                bg="gray.50"
                border="1px solid"
                borderColor="gray.200"
                fontSize="sm"
                minH="120px"
                resize="none"
              />
            </VStack>
          </>
        )}

        {/* Lead Capture Settings */}
        {chatbot.isLeadCaptureActive && (
          <>
            <Separator />
            <VStack align="stretch" gap={4}>
              <HStack>
                <LuUsers size={20} color="#6B46C1" />
                <Text fontSize="md" fontWeight="semibold" color="gray.700">
                  Configurações de Captura de Lead
                </Text>
              </HStack>

              <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
                <GridItem>
                  <VStack align="start" gap={2}>
                    <Text fontSize="sm" color="gray.600" fontWeight="medium">
                      Limite de Mensagens para Trigger:
                    </Text>
                    <Text fontSize="md" color="gray.800" fontWeight="semibold">
                      {chatbot.leadTriggerMessageLimit}
                    </Text>
                  </VStack>
                </GridItem>

                <GridItem>
                  <VStack align="start" gap={2}>
                    <Text fontSize="sm" color="gray.600" fontWeight="medium">
                      Campos Coletados:
                    </Text>
                    <HStack wrap="wrap" gap={2}>
                      {chatbot.leadCaptureJson?.collectName && (
                        <Badge colorScheme="blue" variant="outline" size="sm">
                          Nome
                        </Badge>
                      )}
                      {chatbot.leadCaptureJson?.collectEmail && (
                        <Badge colorScheme="green" variant="outline" size="sm">
                          Email
                        </Badge>
                      )}
                      {chatbot.leadCaptureJson?.collectPhone && (
                        <Badge colorScheme="purple" variant="outline" size="sm">
                          Telefone
                        </Badge>
                      )}
                      {chatbot.leadCaptureJson?.collectCPF && (
                        <Badge colorScheme="orange" variant="outline" size="sm">
                          CPF
                        </Badge>
                      )}
                    </HStack>
                  </VStack>
                </GridItem>
              </Grid>

              <VStack align="stretch" gap={3}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Mensagem de Captura de Lead:
                </Text>
                <Box
                  bg="gray.50"
                  p={3}
                  borderRadius="md"
                  border="1px solid"
                  borderColor="gray.200"
                >
                  <Text fontSize="sm" color="gray.700">
                    {chatbot.leadCaptureMessage || "Não configurada"}
                  </Text>
                </Box>
              </VStack>

              <VStack align="stretch" gap={3}>
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Mensagem de Agradecimento:
                </Text>
                <Box
                  bg="gray.50"
                  p={3}
                  borderRadius="md"
                  border="1px solid"
                  borderColor="gray.200"
                >
                  <Text fontSize="sm" color="gray.700">
                    {chatbot.leadCaptureThankYouMessage || "Não configurada"}
                  </Text>
                </Box>
              </VStack>
            </VStack>
          </>
        )}
      </VStack>
    </Box>
  );
}
