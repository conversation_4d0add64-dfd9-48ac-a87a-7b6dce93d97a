import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  Separator,
  Center,
} from "@chakra-ui/react";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuBrain, LuFileText, LuCalendar, LuDatabase } from "react-icons/lu";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

type KnowledgeBaseDisplayProps = {
  chatbot: BackofficeChatbotDetailDto;
};

export default function KnowledgeBaseDisplay({ chatbot }: KnowledgeBaseDisplayProps) {
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  const formatContentPreview = (content: string, maxLength: number = 200) => {
    // Remove HTML tags for preview
    const textContent = content.replace(/<[^>]*>/g, '');
    if (textContent.length <= maxLength) {
      return textContent;
    }
    return textContent.substring(0, maxLength) + '...';
  };

  const getContentWordCount = (content: string) => {
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.split(/\s+/).filter(word => word.length > 0).length;
  };

  if (!chatbot.knowledgeBase) {
    return (
      <Box
        bg="white"
        borderRadius="xl"
        p={6}
        shadow="sm"
        border="1px solid"
        borderColor="gray.200"
      >
        <VStack align="stretch" gap={4}>
          <HStack>
            <LuBrain size={24} color="#6B46C1" />
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              Base de Conhecimento
            </Text>
          </HStack>

          <Separator />

          <Center py={10}>
            <VStack>
              <LuFileText size={48} color="#D1D5DB" />
              <Text color="gray.500" fontSize="lg">
                Nenhuma base de conhecimento configurada
              </Text>
              <Text color="gray.400" fontSize="sm" textAlign="center">
                Este chatbot não possui uma base de conhecimento associada
              </Text>
            </VStack>
          </Center>
        </VStack>
      </Box>
    );
  }

  const knowledgeBase = chatbot.knowledgeBase;

  return (
    <Box
      bg="white"
      borderRadius="xl"
      p={6}
      shadow="sm"
      border="1px solid"
      borderColor="gray.200"
    >
      <VStack align="stretch" gap={6}>
        {/* Header */}
        <HStack justify="space-between" align="center">
          <HStack>
            <LuBrain size={24} color="#6B46C1" />
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              Base de Conhecimento
            </Text>
          </HStack>
          
          <Badge
            colorScheme={knowledgeBase.isActive ? "green" : "red"}
            variant="subtle"
            fontSize="sm"
          >
            {knowledgeBase.isActive ? "Ativa" : "Inativa"}
          </Badge>
        </HStack>

        <Separator />

        {/* Knowledge Base Info */}
        <VStack align="stretch" gap={4}>
          <HStack justify="space-between" align="start">
            <VStack align="start" gap={2}>
              <HStack>
                <LuDatabase size={16} color="#6B7280" />
                <Text fontSize="sm" color="gray.600" fontWeight="medium">
                  Nome da Coleção:
                </Text>
              </HStack>
              <Text fontSize="md" color="gray.800" fontWeight="semibold">
                {knowledgeBase.collectionName}
              </Text>
            </VStack>

            <VStack align="end" gap={2}>
              <Text fontSize="sm" color="gray.600" fontWeight="medium">
                Tamanho do Chunk:
              </Text>
              <Badge colorScheme="blue" variant="outline">
                {knowledgeBase.chunkSize} caracteres
              </Badge>
            </VStack>
          </HStack>

          {/* Content Statistics */}
          <HStack justify="space-between" align="center">
            <VStack align="start" gap={1}>
              <Text fontSize="sm" color="gray.600" fontWeight="medium">
                Estatísticas do Conteúdo:
              </Text>
              <HStack gap={4}>
                <Text fontSize="sm" color="gray.700">
                  <strong>{getContentWordCount(knowledgeBase.content)}</strong> palavras
                </Text>
                <Text fontSize="sm" color="gray.700">
                  <strong>{knowledgeBase.content.length}</strong> caracteres
                </Text>
              </HStack>
            </VStack>
          </HStack>

          {/* Content Preview */}
          <VStack align="stretch" gap={3}>
            <Text fontSize="sm" color="gray.600" fontWeight="medium">
              Prévia do Conteúdo:
            </Text>
            <Box
              bg="gray.50"
              p={4}
              borderRadius="md"
              border="1px solid"
              borderColor="gray.200"
              maxH="300px"
              overflowY="auto"
            >
              <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                {formatContentPreview(knowledgeBase.content, 500)}
              </Text>
              {knowledgeBase.content.length > 500 && (
                <Text fontSize="xs" color="gray.500" mt={2} fontStyle="italic">
                  ... conteúdo truncado para visualização
                </Text>
              )}
            </Box>
          </VStack>

          {/* Dates */}
          <HStack justify="space-between" align="center">
            <HStack>
              <LuCalendar size={16} color="#6B7280" />
              <VStack align="start" gap={0}>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Criada em:
                </Text>
                <Text fontSize="sm" color="gray.700">
                  {formatDate(knowledgeBase.createdAt)}
                </Text>
              </VStack>
            </HStack>

            <HStack>
              <LuCalendar size={16} color="#6B7280" />
              <VStack align="start" gap={0}>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Última atualização:
                </Text>
                <Text fontSize="sm" color="gray.700">
                  {formatDate(knowledgeBase.updatedAt)}
                </Text>
              </VStack>
            </HStack>
          </HStack>
        </VStack>
      </VStack>
    </Box>
  );
}
