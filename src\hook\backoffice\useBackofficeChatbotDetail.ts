import { api } from "@/services/api";
import { BackofficeChatbotDetailDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { useQuery } from "@tanstack/react-query";

async function getBackofficeChatbotDetail(secureId: string) {
  const { data } = await api.get<BackofficeChatbotDetailDto>(`/backoffice/chatbots/${secureId}`);
  return data;
}

export default function useBackofficeChatbotDetail(secureId: string) {
  return useQuery({
    queryFn: async () => await getBackofficeChatbotDetail(secureId),
    queryKey: ["backoffice-chatbot-detail", secureId],
    enabled: !!secureId,
    refetchInterval: 60000, // Refetch a cada 60 segundos
  });
}
