import {
  <PERSON>,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  Separator,
  <PERSON>ton,
  Flex,
  Center,
  Spinner,
} from "@chakra-ui/react";
import { BackofficeChatbotDetailDto, ChatbotMessageDto } from "@/utils/types/DTO/backoffice-chatbots.dto";
import { LuMessageSquare, LuClock, LuUser, LuBot, LuChevronLeft, LuChevronRight } from "react-icons/lu";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useState } from "react";

type MessageHistoryProps = {
  chatbot: BackofficeChatbotDetailDto;
  isLoading?: boolean;
};

export default function MessageHistory({ chatbot, isLoading }: MessageHistoryProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [showSentMessages, setShowSentMessages] = useState(true);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ptBR });
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case "text":
        return <LuMessageSquare size={16} />;
      case "audio":
        return "🎵";
      case "file":
        return "📎";
      case "image":
        return "🖼️";
      default:
        return <LuMessageSquare size={16} />;
    }
  };

  const currentMessages = showSentMessages ? chatbot.sentMessages : chatbot.receivedMessages;
  const totalPages = currentMessages.meta.totalPages;
  const totalItems = currentMessages.meta.totalItems;

  if (isLoading) {
    return (
      <Box
        bg="white"
        borderRadius="xl"
        p={6}
        shadow="sm"
        border="1px solid"
        borderColor="gray.200"
      >
        <Center h="200px">
          <Spinner size="lg" color="purple.500" />
        </Center>
      </Box>
    );
  }

  return (
    <Box
      bg="white"
      borderRadius="xl"
      p={6}
      shadow="sm"
      border="1px solid"
      borderColor="gray.200"
    >
      <VStack align="stretch" gap={6}>
        {/* Header */}
        <HStack justify="space-between" align="center">
          <HStack>
            <LuMessageSquare size={24} color="#6B46C1" />
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              Histórico de Mensagens
            </Text>
          </HStack>
          
          {/* Toggle buttons */}
          <HStack>
            <Button
              size="sm"
              variant={showSentMessages ? "solid" : "outline"}
              colorScheme="purple"
              onClick={() => setShowSentMessages(true)}
            >
              Enviadas ({chatbot.sentMessages.meta.totalItems})
            </Button>
            <Button
              size="sm"
              variant={!showSentMessages ? "solid" : "outline"}
              colorScheme="blue"
              onClick={() => setShowSentMessages(false)}
            >
              Recebidas ({chatbot.receivedMessages.meta.totalItems})
            </Button>
          </HStack>
        </HStack>

        <Separator />

        {/* Messages List */}
        <VStack align="stretch" gap={4} minH="400px">
          {currentMessages.data.length > 0 ? (
            currentMessages.data.map((message, index) => (
              <Box
                key={message.secureId}
                p={4}
                bg={showSentMessages ? "purple.50" : "blue.50"}
                borderRadius="lg"
                border="1px solid"
                borderColor={showSentMessages ? "purple.200" : "blue.200"}
              >
                <VStack align="stretch" gap={3}>
                  {/* Message header */}
                  <HStack justify="space-between" align="center">
                    <HStack>
                      {showSentMessages ? (
                        <LuBot size={16} color="#6B46C1" />
                      ) : (
                        <LuUser size={16} color="#2563EB" />
                      )}
                      <Badge
                        colorScheme={showSentMessages ? "purple" : "blue"}
                        variant="subtle"
                        fontSize="xs"
                      >
                        {showSentMessages ? "Chatbot" : message.customerName || "Cliente"}
                      </Badge>
                      <HStack>
                        {getMessageTypeIcon(message.messageType)}
                        <Text fontSize="xs" color="gray.600">
                          {message.messageType}
                        </Text>
                      </HStack>
                    </HStack>
                    
                    <HStack>
                      <LuClock size={14} color="#6B7280" />
                      <Text fontSize="xs" color="gray.600">
                        {formatDate(message.createdAt)}
                      </Text>
                    </HStack>
                  </HStack>

                  {/* Message content */}
                  <Box>
                    {message.messageType === "text" ? (
                      <Text fontSize="sm" color="gray.700" whiteSpace="pre-wrap">
                        {message.content}
                      </Text>
                    ) : message.urlFile ? (
                      <HStack>
                        <Text fontSize="sm" color="gray.700">
                          Arquivo anexado:
                        </Text>
                        <Text
                          fontSize="sm"
                          color="blue.600"
                          textDecoration="underline"
                          cursor="pointer"
                          onClick={() => window.open(message.urlFile, "_blank")}
                        >
                          Visualizar arquivo
                        </Text>
                      </HStack>
                    ) : (
                      <Text fontSize="sm" color="gray.500" fontStyle="italic">
                        Conteúdo não disponível
                      </Text>
                    )}
                  </Box>

                  {/* Session info */}
                  {message.sessionId && (
                    <Text fontSize="xs" color="gray.500">
                      Sessão: {message.sessionId}
                    </Text>
                  )}
                </VStack>
              </Box>
            ))
          ) : (
            <Center py={10}>
              <VStack>
                <LuMessageSquare size={48} color="#D1D5DB" />
                <Text color="gray.500" fontSize="lg">
                  Nenhuma mensagem {showSentMessages ? "enviada" : "recebida"}
                </Text>
                <Text color="gray.400" fontSize="sm">
                  Este chatbot ainda não possui mensagens {showSentMessages ? "enviadas" : "recebidas"}
                </Text>
              </VStack>
            </Center>
          )}
        </VStack>

        {/* Pagination */}
        {totalPages > 1 && (
          <>
            <Separator />
            <Flex justify="space-between" align="center">
              <Text fontSize="sm" color="gray.600">
                Mostrando {currentMessages.data.length} de {totalItems} mensagens
              </Text>
              
              <HStack>
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  <LuChevronLeft />
                  Anterior
                </Button>
                
                <Text fontSize="sm" color="gray.600">
                  Página {currentPage} de {totalPages}
                </Text>
                
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                >
                  Próxima
                  <LuChevronRight />
                </Button>
              </HStack>
            </Flex>
          </>
        )}
      </VStack>
    </Box>
  );
}
