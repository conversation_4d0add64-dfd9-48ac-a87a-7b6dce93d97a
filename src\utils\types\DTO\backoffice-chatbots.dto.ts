import { MetaDTO } from "./meta.dto";
import {
  EnumChatBotEmotionalTone,
  EnumChatBotMood,
  EnumChatBotResponseStyle,
} from "@/utils/enums/chat-bots.enums";

export type GetBackofficeChatbotsDto = {
  meta: MetaDTO;
  data: BackofficeChatbotDto[];
};

export type ChatbotConfigurationDto {
  emotionalTone: string | null;
  mood: string | null;
  responseSize: string | null;
  responseStyle: string | null;
  temperature: number;
  isLeadCaptureActive: boolean;
  leadTriggerMessageLimit: number | null;
  leadCaptureMessage: string | null;
  leadCaptureThankYouMessage: string | null;
  greetingMessage: string | null;
  AIPrompt: string | null;
  leadCaptureJson: any;
}

export type ChatbotMessageDto {
  secureId: string;
  sendMessage: string | null;
  receiveMessage: string | null;
  messageDirection: string | null;
  sessionSecureId: string;
  customerName: string | null;
  customerId: string;
  inputToken: number | null;
  outputToken: number | null;
  createdAt: Date;
}

export type BackofficeChatbotDto = {
  // Basic chatbot information
  secureId: string;
  name: string;
  isAI: boolean;
  inputTokenCount: number;
  outputTokenCount: number;
  accountOwnerName: string;
  totalMessagesSent: number;
  totalSessionsResponded: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Detailed configuration
  configuration: ChatbotConfigurationDto;

  // Message history for analysis
  sentMessages: IWithPagination<ChatbotMessageDto>;
  receivedMessages: IWithPagination<ChatbotMessageDto>;
};

// Detailed chatbot view DTO
export type BackofficeChatbotDetailDto = {
  secureId: string;
  name: string;
  isAI: boolean;
  isActive: boolean;
  isLeadCaptureActive: boolean;

  // Configuration
  emotionalTone: EnumChatBotEmotionalTone;
  mood: EnumChatBotMood;
  responseStyle: EnumChatBotResponseStyle;
  responseSize: EnumChatBotResponseStyle;
  temperature: number;
  greetingMessage: string;

  // Lead capture settings
  leadTriggerMessageLimit: number;
  leadCaptureMessage: string;
  leadCaptureThankYouMessage: string;
  leadCaptureJson?: {
    collectName?: boolean;
    collectEmail?: boolean;
    collectPhone?: boolean;
    collectCPF?: boolean;
  };

  // Account information
  account: {
    secureId: string;
    companyName: string;
    prompt?: string;
  };

  // Statistics
  statistics: {
    inputTokenCount: number;
    outputTokenCount: number;
    totalMessagesSent: number;
    totalSessionsResponded: number;
  };

  // Messages
  sentMessages: {
    meta: MetaDTO;
    data: ChatbotMessageDto[];
  };

  receivedMessages: {
    meta: MetaDTO;
    data: ChatbotMessageDto[];
  };

  // Knowledge base
  knowledgeBase?: {
    secureId: string;
    collectionName: string;
    content: string;
    chunkSize: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };

  createdAt: string;
  updatedAt: string;
};

export type ChatbotMessageDto = {
  secureId: string;
  content: string;
  messageType: "text" | "audio" | "file" | "image";
  messageDirection: "sent" | "received";
  sessionId?: string;
  customerName?: string;
  urlFile?: string;
  createdAt: string;
};
