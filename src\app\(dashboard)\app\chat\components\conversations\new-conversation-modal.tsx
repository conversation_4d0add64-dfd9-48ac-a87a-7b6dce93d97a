import { InputSelect } from "@/components/global/inputs/input-select";
import BasicModal from "@/components/global/modal/basic-modal";
import { Text, VStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import * as yup from "yup";

const StartNewConversationModalSchema = yup.object().shape({
  contactSecureId: yup.string().required("Contato é obrigatório"),
});

type StartNewConversationModalFormData = yup.InferType<typeof StartNewConversationModalSchema>;

type StartNewConversationModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function StartNewConversationModal({
  open,
  setOpen,
}: StartNewConversationModalProps) {

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<StartNewConversationModalFormData>({
    resolver: yupResolver(StartNewConversationModalSchema),
  });

  return (
     <BasicModal
      open={open}
      setOpen={setOpen}
      title="Iniciar nova sessão"
      closeOnInteractOutside={true}
      closeOnEscape={true}
      children={
        <VStack>
          <VStack justify={"start"} align={"start"} w={"100%"}>
            <InputSelect
              control={control}
              {...register("contactSecureId")}
              label="Quem deseja contactar?"
              placeholder="Selecione o contato"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              itensList={[
                {
                  label: "Ricardo Cassemiro",
                  value: "1",
                },
                {
                  label: "João da Silva",
                  value: "2",
                },
              ]}
            />
          </VStack>
        </VStack>
      }
    />
  );
}
