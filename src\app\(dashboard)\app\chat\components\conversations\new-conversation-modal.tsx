import { InputSelect } from "@/components/global/inputs/input-select";
import BasicModal from "@/components/global/modal/basic-modal";
import { Button } from "@/components/ui/button";
import { toaster } from "@/components/ui/toaster";
import useWhatsappIntegrations from "@/hook/config/useWhatsappIntegrations";
import { useGetAllContacts } from "@/hook/contacts/useGetAllContacts";
import { api } from "@/services/api";
import { formatCellphone } from "@/utils/funcs/format-number-phone";
import { HStack, Text, VStack } from "@chakra-ui/react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { LuCheck } from "react-icons/lu";
import * as yup from "yup";

const StartNewConversationModalSchema = yup.object().shape({
  contactSecureId: yup.string().required("Contato é obrigatório"),
});

type StartNewConversationModalFormData = yup.InferType<typeof StartNewConversationModalSchema>;

type StartNewConversationModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function StartNewConversationModal({
  open,
  setOpen,
}: StartNewConversationModalProps) {
  const { data: contactsData } = useGetAllContacts();
  const contacts = contactsData?.data;

  const contactList = contacts?.map((contact) => ({
    label: contact.name || contact.email || contact.phone || contact.document || "Contato sem nome",
    value: contact.secureId,
  }));

  const {
    register,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<StartNewConversationModalFormData>({
    resolver: yupResolver(StartNewConversationModalSchema),
  });

  const selectedContactSecureIdArray = watch("contactSecureId");
  const selectedContactSecureId = Array.isArray(selectedContactSecureIdArray)
    ? selectedContactSecureIdArray[0]
    : selectedContactSecureIdArray;
  
  // Daqui pra frente é igual ao create-new-session.tsx
  const { data, isLoading } = useWhatsappIntegrations({
    onlyType: "messager",
  });
  const router = useRouter();

  const mutation = useMutation({
    mutationFn: async ({
      whatsappIntegrationSecureId,
      isBusiness,
    }: {
      whatsappIntegrationSecureId: string;
      isBusiness: boolean;
    }) => {
      const { data } = await api.post<{ sessionSecureId: string }>("/chat-sessions", {
        contactSecureId: selectedContactSecureId,
        whatsappIntegrationSecureId,
        isBusiness: isBusiness,
      });
      return data.sessionSecureId;
    },
    onSuccess: (sessionSecureId) => {
      setOpen(false);
      router.push(`/app/chat/${sessionSecureId}`);
      // Força reload após o push para garantir montagem do provider
      toaster.success({
        title: "Sessão iniciada",
        description: "Você foi redirecionado para o chat.",
      });
    },
    onError: () => {
      toaster.error({
        title: "Erro ao iniciar sessão",
        description: "Não foi possível iniciar a sessão. Tente novamente.",
      });
    },
  });

  return (
     <BasicModal
      open={open}
      setOpen={setOpen}
      title="Iniciar nova sessão"
      closeOnInteractOutside={true}
      closeOnEscape={true}
      children={
        <VStack>
          <VStack justify={"start"} align={"start"} w={"100%"}>
            <InputSelect
              control={control}
              {...register("contactSecureId")}
              label="Quem você deseja contactar?"
              placeholder="Selecione o contato"
              labelColor={"chatTextColor"}
              rounded={"2xl"}
              itensList={contactList || []}
            />
            <Text fontSize={"sm"} mt={4}>
              Selecione o número para iniciar a conversa.
            </Text>
            <Text fontSize={"sm"} fontStyle={"italic"} color={"gray.500"}>
              Lembre-se: Apenas números do WhatsApp Messenger estão disponíveis para iniciar conversas. Para números WhatsApp Business, o início da conversa deve ser feito através de templates.
            </Text>
          </VStack>

          <VStack w={"100%"} mt={4} gap={2}>
            {isLoading ? (
              <Text fontSize={"sm"}>Carregando...</Text>
            ) : (
              data?.data.map((integration) => (
                <HStack
                  key={integration.secureId}
                  justifyContent={"space-between"}
                  w={"100%"}
                  bgColor={"gray.50"}
                  rounded={"md"}
                >
                  <Button
                    bgColor={"chatPrimary"}
                    _hover={{
                      bgColor: "chatCardBackground",
                      border: "1px solid",
                      borderColor: "chatPrimary",
                      color: "chatPrimary",
                    }}
                    _active={{
                      transform: "scale(0.95)",
                      animationDelay: "0.2s",
                    }}
                    color={"white"}
                    size={"sm"}
                    rounded={"lg"}
                    loading={mutation.isPending}
                    onClick={() =>
                      mutation.mutate({
                        whatsappIntegrationSecureId: integration.secureId,
                        isBusiness: integration.isBusiness,
                      })
                    }
                  >
                    <LuCheck />
                  </Button>
                  <HStack w={"100%"} justifyContent={"flex-start"} gap={10}>
                    <Text fontSize={"md"} w={"50%"}>
                      {integration.phoneNumber
                        ? formatCellphone(integration.phoneNumber)
                        : "-"}
                    </Text>
                    <Text fontSize={"md"} textAlign={"start"} w={"50%"}>
                      {integration.isBusiness
                        ? "WhatsApp Business"
                        : "Whatsapp"}
                    </Text>
                  </HStack>
                </HStack>
              ))
            )}
          </VStack>
        </VStack>
      }
    />
  );
}
